## Objective: Fix Critical Bugs in Habit Completion Logic

The goal is to completely resolve the bugs preventing users from correctly marking habits as complete on the main habit tracking screen. The current implementation is unreliable and behaves differently depending on the "Start Day of Week" setting.

## Bug Descriptions

The core functionality of tapping a circle to mark a habit complete is broken.

1.  **Interaction Fails When Week Starts on Sunday:**
    * **Problem:** When the user has set the "Start Day of Week" to Sunday, tapping on any habit completion circle has no effect. The circle's color does not change, and no completion percentages are updated. The interaction is completely disabled.

2.  **Erratic Behavior When Week Starts on Monday:**
    * **Problem:** When the "Start Day of Week" is set to Monday, the interaction works incorrectly and unpredictably. Tapping one circle can cause multiple other circles in different rows or columns to change state. Deselecting one item can also cause others to become selected.
    * **Conclusion:** The logic fails to isolate the click event to the specific item that was tapped.

## Implementation Plan: Refactor Habit Click Handling

The existing implementation is fundamentally flawed. We must refactor the component responsible for handling user taps on the habit completion circles.

### 1. Pinpoint and Isolate the Click Listener

* **Goal:** Identify the exact `onClickListener` within the view adapter that is responsible for managing the grid of habit completion circles. All subsequent changes will be focused here.

### 2. Implement Accurate Data Mapping on Click

* **Goal:** When a circle is tapped, the listener must be able to precisely identify the context of the click.
* **Logic:**
    * The listener must correctly retrieve the unique **ID of the habit** corresponding to its row.
    * The listener must also correctly retrieve the specific **date** corresponding to its column.
    * This combination of `habitId` and `date` is essential for accurately targeting the correct data point to update.

### 3. Refactor the State Update Logic

* **Goal:** Ensure that a click updates only the single, correct habit completion record.
* **Logic:**
    1.  Upon detecting a tap, the listener should pass the `habitId` and `date` to the appropriate business logic layer (e.g., the ViewModel).
    2.  This layer will then execute the database or state update for that **single record only**.
    3.  The UI should then be notified to refresh its state. The update should be efficient and, if possible, only redraw the specific item that was changed to prevent the "cascading selection" bug.

### 4. Ensure Consistency and Test

* **Goal:** The final implementation must work identically regardless of user settings.
* **Testing Requirement:** Verify the following scenarios:
    * The fix works perfectly when the "Start Day of Week" is set to **Sunday**.
    * The fix works perfectly when the "Start Day of Week" is set to **Monday**.
    * Tapping a circle for one habit has **zero effect** on any other habit in the list.
    * All percentage calculations ("This Day," "This Week") update correctly immediately after a state change.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.