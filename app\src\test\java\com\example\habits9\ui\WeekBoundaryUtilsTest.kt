package com.example.habits9.ui

import com.example.habits9.ui.MainViewModel.WeekBoundaryUtils
import org.junit.Test
import org.junit.Assert.*
import java.time.LocalDate
import java.time.DayOfWeek

/**
 * Unit tests for WeekBoundaryUtils to validate week calculation fixes
 */
class WeekBoundaryUtilsTest {

    @Test
    fun testGetWeekStart_Sunday_FirstDayOfWeek() {
        // Test with Sunday as first day of week
        val monday = LocalDate.of(2024, 1, 8) // Monday, January 8, 2024
        val tuesday = LocalDate.of(2024, 1, 9) // Tuesday, January 9, 2024
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val thursday = LocalDate.of(2024, 1, 11) // Thursday, January 11, 2024
        val friday = LocalDate.of(2024, 1, 12) // Friday, January 12, 2024
        val saturday = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024
        val sunday = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024
        
        val expectedWeekStart = LocalDate.of(2024, 1, 7) // Previous Sunday, January 7, 2024
        
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(monday, "SUNDAY"))
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(tuesday, "SUNDAY"))
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(wednesday, "SUNDAY"))
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(thursday, "SUNDAY"))
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(friday, "SUNDAY"))
        assertEquals(expectedWeekStart, WeekBoundaryUtils.getWeekStart(saturday, "SUNDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekStart(sunday, "SUNDAY")) // Sunday should return itself
    }

    @Test
    fun testGetWeekStart_Monday_FirstDayOfWeek() {
        // Test with Monday as first day of week
        val monday = LocalDate.of(2024, 1, 8) // Monday, January 8, 2024
        val tuesday = LocalDate.of(2024, 1, 9) // Tuesday, January 9, 2024
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val thursday = LocalDate.of(2024, 1, 11) // Thursday, January 11, 2024
        val friday = LocalDate.of(2024, 1, 12) // Friday, January 12, 2024
        val saturday = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024
        val sunday = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024
        
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(monday, "MONDAY")) // Monday should return itself
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(tuesday, "MONDAY"))
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(wednesday, "MONDAY"))
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(thursday, "MONDAY"))
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(friday, "MONDAY"))
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(saturday, "MONDAY"))
        assertEquals(monday, WeekBoundaryUtils.getWeekStart(sunday, "MONDAY"))
    }

    @Test
    fun testGetWeekEnd_Sunday_FirstDayOfWeek() {
        // Test with Sunday as first day of week (week ends on Saturday)
        val monday = LocalDate.of(2024, 1, 8) // Monday, January 8, 2024
        val tuesday = LocalDate.of(2024, 1, 9) // Tuesday, January 9, 2024
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val thursday = LocalDate.of(2024, 1, 11) // Thursday, January 11, 2024
        val friday = LocalDate.of(2024, 1, 12) // Friday, January 12, 2024
        val saturday = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024
        val sunday = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024
        
        val expectedWeekEnd = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024
        
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(monday, "SUNDAY"))
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(tuesday, "SUNDAY"))
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(wednesday, "SUNDAY"))
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(thursday, "SUNDAY"))
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(friday, "SUNDAY"))
        assertEquals(expectedWeekEnd, WeekBoundaryUtils.getWeekEnd(saturday, "SUNDAY")) // Saturday should return itself
        
        val nextSaturday = LocalDate.of(2024, 1, 20) // Next Saturday, January 20, 2024
        assertEquals(nextSaturday, WeekBoundaryUtils.getWeekEnd(sunday, "SUNDAY"))
    }

    @Test
    fun testGetWeekEnd_Monday_FirstDayOfWeek() {
        // Test with Monday as first day of week (week ends on Sunday)
        val monday = LocalDate.of(2024, 1, 8) // Monday, January 8, 2024
        val tuesday = LocalDate.of(2024, 1, 9) // Tuesday, January 9, 2024
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val thursday = LocalDate.of(2024, 1, 11) // Thursday, January 11, 2024
        val friday = LocalDate.of(2024, 1, 12) // Friday, January 12, 2024
        val saturday = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024
        val sunday = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024
        
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(monday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(tuesday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(wednesday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(thursday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(friday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(saturday, "MONDAY"))
        assertEquals(sunday, WeekBoundaryUtils.getWeekEnd(sunday, "MONDAY")) // Sunday should return itself
    }

    @Test
    fun testGetCurrentWeekDates_Sunday_FirstDayOfWeek() {
        // Test getting all 7 days of the week with Sunday as first day
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val weekDates = WeekBoundaryUtils.getCurrentWeekDates(wednesday, "SUNDAY")
        
        assertEquals(7, weekDates.size)
        assertEquals(LocalDate.of(2024, 1, 7), weekDates[0]) // Sunday
        assertEquals(LocalDate.of(2024, 1, 8), weekDates[1]) // Monday
        assertEquals(LocalDate.of(2024, 1, 9), weekDates[2]) // Tuesday
        assertEquals(LocalDate.of(2024, 1, 10), weekDates[3]) // Wednesday
        assertEquals(LocalDate.of(2024, 1, 11), weekDates[4]) // Thursday
        assertEquals(LocalDate.of(2024, 1, 12), weekDates[5]) // Friday
        assertEquals(LocalDate.of(2024, 1, 13), weekDates[6]) // Saturday
    }

    @Test
    fun testGetCurrentWeekDates_Monday_FirstDayOfWeek() {
        // Test getting all 7 days of the week with Monday as first day
        val wednesday = LocalDate.of(2024, 1, 10) // Wednesday, January 10, 2024
        val weekDates = WeekBoundaryUtils.getCurrentWeekDates(wednesday, "MONDAY")
        
        assertEquals(7, weekDates.size)
        assertEquals(LocalDate.of(2024, 1, 8), weekDates[0]) // Monday
        assertEquals(LocalDate.of(2024, 1, 9), weekDates[1]) // Tuesday
        assertEquals(LocalDate.of(2024, 1, 10), weekDates[2]) // Wednesday
        assertEquals(LocalDate.of(2024, 1, 11), weekDates[3]) // Thursday
        assertEquals(LocalDate.of(2024, 1, 12), weekDates[4]) // Friday
        assertEquals(LocalDate.of(2024, 1, 13), weekDates[5]) // Saturday
        assertEquals(LocalDate.of(2024, 1, 14), weekDates[6]) // Sunday
    }

    @Test
    fun testGetWeekNumber_Sunday_FirstDayOfWeek() {
        // Test week number calculation with Sunday as first day of week
        val date1 = LocalDate.of(2024, 1, 7) // Sunday, January 7, 2024 (should be week 2)
        val date2 = LocalDate.of(2024, 1, 13) // Saturday, January 13, 2024 (should be week 2)
        val date3 = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024 (should be week 3)
        
        val week1 = WeekBoundaryUtils.getWeekNumber(date1, "SUNDAY")
        val week2 = WeekBoundaryUtils.getWeekNumber(date2, "SUNDAY")
        val week3 = WeekBoundaryUtils.getWeekNumber(date3, "SUNDAY")
        
        assertEquals(week1, week2) // Same week
        assertEquals(week3, week1 + 1) // Next week
    }

    @Test
    fun testGetWeekNumber_Monday_FirstDayOfWeek() {
        // Test week number calculation with Monday as first day of week
        val date1 = LocalDate.of(2024, 1, 8) // Monday, January 8, 2024
        val date2 = LocalDate.of(2024, 1, 14) // Sunday, January 14, 2024 (same week)
        val date3 = LocalDate.of(2024, 1, 15) // Monday, January 15, 2024 (next week)
        
        val week1 = WeekBoundaryUtils.getWeekNumber(date1, "MONDAY")
        val week2 = WeekBoundaryUtils.getWeekNumber(date2, "MONDAY")
        val week3 = WeekBoundaryUtils.getWeekNumber(date3, "MONDAY")
        
        assertEquals(week1, week2) // Same week
        assertEquals(week3, week1 + 1) // Next week
    }
}
