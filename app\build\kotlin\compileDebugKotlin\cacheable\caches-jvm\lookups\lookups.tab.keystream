  Activity android.app  Application android.app  Build android.app.Activity  CreateMeasurableHabitScreen android.app.Activity  CreateYesNoHabitScreen android.app.Activity  HabitDetailsScreen android.app.Activity  HabitTypeSelectionScreen android.app.Activity  
HomeScreen android.app.Activity  ManageSectionsScreen android.app.Activity  NavHost android.app.Activity  SettingsScreen android.app.Activity  UHabits_99Theme android.app.Activity  
composable android.app.Activity  onCreate android.app.Activity  rememberNavController android.app.Activity  
setContent android.app.Activity  Context android.content  Build android.content.Context  CreateMeasurableHabitScreen android.content.Context  CreateYesNoHabitScreen android.content.Context  HabitDetailsScreen android.content.Context  HabitTypeSelectionScreen android.content.Context  
HomeScreen android.content.Context  ManageSectionsScreen android.content.Context  NavHost android.content.Context  SettingsScreen android.content.Context  UHabits_99Theme android.content.Context  
composable android.content.Context  	dataStore android.content.Context  rememberNavController android.content.Context  
setContent android.content.Context  Build android.content.ContextWrapper  CreateMeasurableHabitScreen android.content.ContextWrapper  CreateYesNoHabitScreen android.content.ContextWrapper  HabitDetailsScreen android.content.ContextWrapper  HabitTypeSelectionScreen android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  ManageSectionsScreen android.content.ContextWrapper  NavHost android.content.ContextWrapper  SettingsScreen android.content.ContextWrapper  UHabits_99Theme android.content.ContextWrapper  
composable android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  Build  android.view.ContextThemeWrapper  CreateMeasurableHabitScreen  android.view.ContextThemeWrapper  CreateYesNoHabitScreen  android.view.ContextThemeWrapper  HabitDetailsScreen  android.view.ContextThemeWrapper  HabitTypeSelectionScreen  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  ManageSectionsScreen  android.view.ContextThemeWrapper  NavHost  android.view.ContextThemeWrapper  SettingsScreen  android.view.ContextThemeWrapper  UHabits_99Theme  android.view.ContextThemeWrapper  
composable  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CreateMeasurableHabitScreen #androidx.activity.ComponentActivity  CreateYesNoHabitScreen #androidx.activity.ComponentActivity  HabitDetailsScreen #androidx.activity.ComponentActivity  HabitTypeSelectionScreen #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  ManageSectionsScreen #androidx.activity.ComponentActivity  NavHost #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  SettingsScreen #androidx.activity.ComponentActivity  UHabits_99Theme #androidx.activity.ComponentActivity  
composable #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  CreateMeasurableHabitScreen -androidx.activity.ComponentActivity.Companion  CreateYesNoHabitScreen -androidx.activity.ComponentActivity.Companion  HabitDetailsScreen -androidx.activity.ComponentActivity.Companion  HabitTypeSelectionScreen -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  ManageSectionsScreen -androidx.activity.ComponentActivity.Companion  NavHost -androidx.activity.ComponentActivity.Companion  SettingsScreen -androidx.activity.ComponentActivity.Companion  UHabits_99Theme -androidx.activity.ComponentActivity.Companion  
composable -androidx.activity.ComponentActivity.Companion  rememberNavController -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  RequiresApi androidx.annotation  AnimatedContentScope androidx.compose.animation  animateContentSize androidx.compose.animation  CreateMeasurableHabitScreen /androidx.compose.animation.AnimatedContentScope  CreateYesNoHabitScreen /androidx.compose.animation.AnimatedContentScope  HabitDetailsScreen /androidx.compose.animation.AnimatedContentScope  HabitTypeSelectionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  ManageSectionsScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  animateDpAsState androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  Canvas androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  horizontalScroll androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation   detectDragGesturesAfterLongPress $androidx.compose.foundation.gestures  
AccentPrimary "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CreateHabitViewModel "androidx.compose.foundation.layout  DarkBackground "androidx.compose.foundation.layout  	DayOfWeek "androidx.compose.foundation.layout  DividerColor "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  FrequencyOption "androidx.compose.foundation.layout  FrequencyRadioOption "androidx.compose.foundation.layout  GhostPlaceholder "androidx.compose.foundation.layout  HabitFrequency "androidx.compose.foundation.layout  HabitSection "androidx.compose.foundation.layout  HapticFeedbackType "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  	LocalTime "androidx.compose.foundation.layout  ManageSectionsViewModel "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RadioButtonDefaults "androidx.compose.foundation.layout  
ReminderState "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  
SectionColors "androidx.compose.foundation.layout  SectionListItem "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SurfaceVariantDark "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  SwitchDefaults "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextPrimary "androidx.compose.foundation.layout  
TextSecondary "androidx.compose.foundation.layout  
TimePicker "androidx.compose.foundation.layout  TimePickerDefaults "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  alpha "androidx.compose.foundation.layout  animateContentSize "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  coerceAtMost "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  indexOfFirst "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  minus "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  pointerInput "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberTimePickerState "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toInt "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  zIndex "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccentPrimary +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  CompletionIndicator +androidx.compose.foundation.layout.BoxScope  DarkBackground +androidx.compose.foundation.layout.BoxScope  DividerColor +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  GhostPlaceholder +androidx.compose.foundation.layout.BoxScope  HapticFeedbackType +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  SectionListItem +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  SurfaceVariantDark +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TextPrimary +androidx.compose.foundation.layout.BoxScope  
TextSecondary +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  animateContentSize +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  coerceAtMost +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
drawBehind +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  find +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  horizontalScroll +androidx.compose.foundation.layout.BoxScope  isWeekStartDay +androidx.compose.foundation.layout.BoxScope  itemsIndexed +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  
plusAssign +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  split +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  
AccentPrimary .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DarkBackground .androidx.compose.foundation.layout.ColumnScope  	DayOfWeek .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  DividerColor .androidx.compose.foundation.layout.ColumnScope  
DragIndicator .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  FrequencyOption .androidx.compose.foundation.layout.ColumnScope  FrequencyRadioOption .androidx.compose.foundation.layout.ColumnScope  FrozenPaneLayout .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MetricView .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Offset .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  OverviewSection .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RadioButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SectionColors .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  SubHeaderRow .androidx.compose.foundation.layout.ColumnScope  SurfaceVariantDark .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  SwitchDefaults .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TextPrimary .androidx.compose.foundation.layout.ColumnScope  
TextSecondary .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope   detectDragGesturesAfterLongPress .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  
drawBehind .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  horizontalScroll .androidx.compose.foundation.layout.ColumnScope  isWeekStartDay .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  minus .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  pointerInput .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  selectableGroup .androidx.compose.foundation.layout.ColumnScope  shadow .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  split .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  zIndex .androidx.compose.foundation.layout.ColumnScope  
AccentPrimary +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  BackgroundDark +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CompletionIndicator +androidx.compose.foundation.layout.RowScope  DarkBackground +androidx.compose.foundation.layout.RowScope  	DateRange +androidx.compose.foundation.layout.RowScope  	DayOfWeek +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  DividerColor +androidx.compose.foundation.layout.RowScope  
DragIndicator +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  FrequencyOption +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MetricView +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  NotificationsOff +androidx.compose.foundation.layout.RowScope  Offset +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RadioButtonDefaults +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  SurfaceVariantDark +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  SwitchDefaults +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextPrimary +androidx.compose.foundation.layout.RowScope  
TextSecondary +androidx.compose.foundation.layout.RowScope  alpha +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  com +androidx.compose.foundation.layout.RowScope   detectDragGesturesAfterLongPress +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
drawBehind +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  horizontalScroll +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  isWeekStartDay +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  minus +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  plus +androidx.compose.foundation.layout.RowScope  pointerInput +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  split +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  example &androidx.compose.foundation.layout.com  habits9 .androidx.compose.foundation.layout.com.example  data 6androidx.compose.foundation.layout.com.example.habits9  HabitSection ;androidx.compose.foundation.layout.com.example.habits9.data  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  LazyListItemInfo  androidx.compose.foundation.lazy  LazyListLayoutInfo  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  
AccentPrimary .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  CircleShape .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyItemScope  DividerColor .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  GhostPlaceholder .androidx.compose.foundation.lazy.LazyItemScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Offset .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  SectionListItem .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TextPrimary .androidx.compose.foundation.lazy.LazyItemScope  
TextSecondary .androidx.compose.foundation.lazy.LazyItemScope  animateContentSize .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  coerceAtMost .androidx.compose.foundation.lazy.LazyItemScope  com .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  
drawBehind .androidx.compose.foundation.lazy.LazyItemScope  find .androidx.compose.foundation.lazy.LazyItemScope  forEachIndexed .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  horizontalScroll .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  
plusAssign .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  index 1androidx.compose.foundation.lazy.LazyListItemInfo  offset 1androidx.compose.foundation.lazy.LazyListItemInfo  size 1androidx.compose.foundation.lazy.LazyListItemInfo  visibleItemsInfo 3androidx.compose.foundation.lazy.LazyListLayoutInfo  
AccentPrimary .androidx.compose.foundation.lazy.LazyListScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  CircleShape .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyListScope  DividerColor .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  GhostPlaceholder .androidx.compose.foundation.lazy.LazyListScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Offset .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  
SectionColors .androidx.compose.foundation.lazy.LazyListScope  SectionListItem .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TextPrimary .androidx.compose.foundation.lazy.LazyListScope  
TextSecondary .androidx.compose.foundation.lazy.LazyListScope  animateContentSize .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  coerceAtMost .androidx.compose.foundation.lazy.LazyListScope  com .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  
drawBehind .androidx.compose.foundation.lazy.LazyListScope  find .androidx.compose.foundation.lazy.LazyListScope  forEachIndexed .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  horizontalScroll .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  
plusAssign .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  
layoutInfo .androidx.compose.foundation.lazy.LazyListState  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	DateRange ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  
DragIndicator ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  NotificationsOff ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DragIndicator &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsOff &androidx.compose.material.icons.filled  
AccentPrimary androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  CreateHabitViewModel androidx.compose.material3  DarkBackground androidx.compose.material3  	DayOfWeek androidx.compose.material3  DividerColor androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FontWeight androidx.compose.material3  FrequencyOption androidx.compose.material3  FrequencyRadioOption androidx.compose.material3  GhostPlaceholder androidx.compose.material3  HabitFrequency androidx.compose.material3  HabitSection androidx.compose.material3  HapticFeedbackType androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  List androidx.compose.material3  	LocalTime androidx.compose.material3  ManageSectionsViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
PaddingValues androidx.compose.material3  RadioButton androidx.compose.material3  RadioButtonColors androidx.compose.material3  RadioButtonDefaults androidx.compose.material3  
ReminderState androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  
SectionColors androidx.compose.material3  SectionListItem androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  SurfaceVariantDark androidx.compose.material3  Switch androidx.compose.material3  SwitchColors androidx.compose.material3  SwitchDefaults androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  TextPrimary androidx.compose.material3  
TextSecondary androidx.compose.material3  
TimePicker androidx.compose.material3  TimePickerColors androidx.compose.material3  TimePickerDefaults androidx.compose.material3  TimePickerState androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  align androidx.compose.material3  alpha androidx.compose.material3  animateContentSize androidx.compose.material3  
background androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  coerceAtMost androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  indexOfFirst androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  minus androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  
plusAssign androidx.compose.material3  pointerInput androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  rememberTimePickerState androidx.compose.material3  setValue androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  takeIf androidx.compose.material3  to androidx.compose.material3  toInt androidx.compose.material3  toIntOrNull androidx.compose.material3  topAppBarColors androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  zIndex androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
AccentPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DividerColor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextFieldDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  SurfaceVariantDark 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TextPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
TextSecondary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
background 6androidx.compose.material3.ExposedDropdownMenuBoxScope  colors 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors .androidx.compose.material3.RadioButtonDefaults  colors )androidx.compose.material3.SwitchDefaults  colors -androidx.compose.material3.TimePickerDefaults  hour *androidx.compose.material3.TimePickerState  minute *androidx.compose.material3.TimePickerState  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  example androidx.compose.material3.com  habits9 &androidx.compose.material3.com.example  data .androidx.compose.material3.com.example.habits9  HabitSection 3androidx.compose.material3.com.example.habits9.data  
AccentPrimary androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Anchor androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CreateHabitViewModel androidx.compose.runtime  DarkBackground androidx.compose.runtime  	DayOfWeek androidx.compose.runtime  DividerColor androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  
FontWeight androidx.compose.runtime  FrequencyOption androidx.compose.runtime  FrequencyRadioOption androidx.compose.runtime  GhostPlaceholder androidx.compose.runtime  HabitFrequency androidx.compose.runtime  HabitSection androidx.compose.runtime  HapticFeedbackType androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  	LocalTime androidx.compose.runtime  ManageSectionsViewModel androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RadioButton androidx.compose.runtime  RadioButtonDefaults androidx.compose.runtime  
ReminderState androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  
SectionColors androidx.compose.runtime  SectionListItem androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SurfaceVariantDark androidx.compose.runtime  Switch androidx.compose.runtime  SwitchDefaults androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TextPrimary androidx.compose.runtime  
TextSecondary androidx.compose.runtime  
TimePicker androidx.compose.runtime  TimePickerDefaults androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  align androidx.compose.runtime  alpha androidx.compose.runtime  animateContentSize androidx.compose.runtime  
background androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  coerceAtMost androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  indexOfFirst androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  minus androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  
plusAssign androidx.compose.runtime  pointerInput androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberTimePickerState androidx.compose.runtime  setValue androidx.compose.runtime  shadow androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  takeIf androidx.compose.runtime  to androidx.compose.runtime  toInt androidx.compose.runtime  toIntOrNull androidx.compose.runtime  topAppBarColors androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  zIndex androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  example androidx.compose.runtime.com  habits9 $androidx.compose.runtime.com.example  data ,androidx.compose.runtime.com.example.habits9  HabitSection 1androidx.compose.runtime.com.example.habits9.data  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  zIndex androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  animateContentSize androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  
drawBehind androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  horizontalScroll androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  selectableGroup androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  animateContentSize &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  horizontalScroll &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  selectableGroup &androidx.compose.ui.Modifier.Companion  shadow &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  
drawBehind androidx.compose.ui.draw  scale androidx.compose.ui.draw  shadow androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  
plusAssign #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  height !androidx.compose.ui.geometry.Size  minDimension !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  scaleX /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  translationY /androidx.compose.ui.graphics.GraphicsLayerScope  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  
AccentPrimary 0androidx.compose.ui.graphics.drawscope.DrawScope  DividerColor 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  
TextSecondary 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  TextHandleMove 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  TextHandleMove ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  consume 4androidx.compose.ui.input.pointer.PointerInputChange   detectDragGesturesAfterLongPress 3androidx.compose.ui.input.pointer.PointerInputScope  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  Role androidx.compose.ui.semantics  	Companion "androidx.compose.ui.semantics.Role  RadioButton "androidx.compose.ui.semantics.Role  RadioButton ,androidx.compose.ui.semantics.Role.Companion  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  plus androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CreateMeasurableHabitScreen #androidx.core.app.ComponentActivity  CreateYesNoHabitScreen #androidx.core.app.ComponentActivity  HabitDetailsScreen #androidx.core.app.ComponentActivity  HabitTypeSelectionScreen #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  ManageSectionsScreen #androidx.core.app.ComponentActivity  NavHost #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  SettingsScreen #androidx.core.app.ComponentActivity  UHabits_99Theme #androidx.core.app.ComponentActivity  
composable #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  CreateMeasurableHabitScreen #androidx.navigation.NavGraphBuilder  CreateYesNoHabitScreen #androidx.navigation.NavGraphBuilder  HabitDetailsScreen #androidx.navigation.NavGraphBuilder  HabitTypeSelectionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  ManageSectionsScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
Completion 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  
ForeignKey 
androidx.room  Habit 
androidx.room  HabitSection 
androidx.room  Insert 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  	Companion androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  
CompletionDao androidx.room.RoomDatabase  HabitDao androidx.room.RoomDatabase  HabitSectionDao androidx.room.RoomDatabase  
addMigrations "androidx.room.RoomDatabase.Builder  build "androidx.room.RoomDatabase.Builder  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  Application com.example.habits9  HabitsApplication com.example.habits9  HiltAndroidApp com.example.habits9  AT_LEAST com.example.habits9.data  AT_MOST com.example.habits9.data  ApplicationContext com.example.habits9.data  Boolean com.example.habits9.data  
Completion com.example.habits9.data  
CompletionDao com.example.habits9.data  CompletionRepository com.example.habits9.data  Context com.example.habits9.data  Dao com.example.habits9.data  	DataStore com.example.habits9.data  Database com.example.habits9.data  Delete com.example.habits9.data  Double com.example.habits9.data  Entity com.example.habits9.data  Flow com.example.habits9.data  
ForeignKey com.example.habits9.data  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  	HabitType com.example.habits9.data  IllegalStateException com.example.habits9.data  Inject com.example.habits9.data  Insert com.example.habits9.data  Int com.example.habits9.data  List com.example.habits9.data  Long com.example.habits9.data  	NUMERICAL com.example.habits9.data  NumericalHabitType com.example.habits9.data  OnConflictStrategy com.example.habits9.data  Preferences com.example.habits9.data  PreferencesKeys com.example.habits9.data  
PrimaryKey com.example.habits9.data  Query com.example.habits9.data  RoomDatabase com.example.habits9.data  	Singleton com.example.habits9.data  String com.example.habits9.data  System com.example.habits9.data  UUID com.example.habits9.data  Update com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  YES_NO com.example.habits9.data  	dataStore com.example.habits9.data  edit com.example.habits9.data  fromInt com.example.habits9.data  map com.example.habits9.data  provideDelegate com.example.habits9.data  replace com.example.habits9.data  stringPreferencesKey com.example.habits9.data  habitId #com.example.habits9.data.Completion  	timestamp #com.example.habits9.data.Completion  OnConflictStrategy &com.example.habits9.data.CompletionDao  deleteAllCompletionsForHabit &com.example.habits9.data.CompletionDao  deleteCompletion &com.example.habits9.data.CompletionDao  getCompletionForHabitAndDate &com.example.habits9.data.CompletionDao  getCompletionsForHabit &com.example.habits9.data.CompletionDao  getCompletionsForHabitsInRange &com.example.habits9.data.CompletionDao  insertCompletion &com.example.habits9.data.CompletionDao  updateCompletion &com.example.habits9.data.CompletionDao  
completionDao -com.example.habits9.data.CompletionRepository  deleteCompletion -com.example.habits9.data.CompletionRepository  getCompletionForHabitAndDate -com.example.habits9.data.CompletionRepository  getCompletionsForHabitsInRange -com.example.habits9.data.CompletionRepository  insertCompletion -com.example.habits9.data.CompletionRepository  	HabitType com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  fromInt com.example.habits9.data.Habit  	habitType com.example.habits9.data.Habit  id com.example.habits9.data.Habit  name com.example.habits9.data.Habit  
targetType com.example.habits9.data.Habit  type com.example.habits9.data.Habit  OnConflictStrategy !com.example.habits9.data.HabitDao  deleteHabit !com.example.habits9.data.HabitDao  getAllHabits !com.example.habits9.data.HabitDao  getHabitById !com.example.habits9.data.HabitDao  insertHabit !com.example.habits9.data.HabitDao  updateHabit !com.example.habits9.data.HabitDao  
completionDao &com.example.habits9.data.HabitDatabase  habitDao &com.example.habits9.data.HabitDatabase  habitSectionDao &com.example.habits9.data.HabitDatabase  getAllHabits (com.example.habits9.data.HabitRepository  habitDao (com.example.habits9.data.HabitRepository  insertHabit (com.example.habits9.data.HabitRepository  color %com.example.habits9.data.HabitSection  copy %com.example.habits9.data.HabitSection  id %com.example.habits9.data.HabitSection  let %com.example.habits9.data.HabitSection  name %com.example.habits9.data.HabitSection  OnConflictStrategy (com.example.habits9.data.HabitSectionDao  deleteHabitSection (com.example.habits9.data.HabitSectionDao  getAllHabitSections (com.example.habits9.data.HabitSectionDao  insertHabitSection (com.example.habits9.data.HabitSectionDao  updateHabitSection (com.example.habits9.data.HabitSectionDao  updateHabitSections (com.example.habits9.data.HabitSectionDao  deleteHabitSection /com.example.habits9.data.HabitSectionRepository  getAllHabitSections /com.example.habits9.data.HabitSectionRepository  habitSectionDao /com.example.habits9.data.HabitSectionRepository  insertHabitSection /com.example.habits9.data.HabitSectionRepository  updateHabitSection /com.example.habits9.data.HabitSectionRepository  updateHabitSections /com.example.habits9.data.HabitSectionRepository  	Companion "com.example.habits9.data.HabitType  	HabitType "com.example.habits9.data.HabitType  IllegalStateException "com.example.habits9.data.HabitType  Int "com.example.habits9.data.HabitType  	NUMERICAL "com.example.habits9.data.HabitType  YES_NO "com.example.habits9.data.HabitType  fromInt "com.example.habits9.data.HabitType  value "com.example.habits9.data.HabitType  IllegalStateException ,com.example.habits9.data.HabitType.Companion  	NUMERICAL ,com.example.habits9.data.HabitType.Companion  YES_NO ,com.example.habits9.data.HabitType.Companion  fromInt ,com.example.habits9.data.HabitType.Companion  AT_LEAST +com.example.habits9.data.NumericalHabitType  AT_MOST +com.example.habits9.data.NumericalHabitType  	Companion +com.example.habits9.data.NumericalHabitType  IllegalStateException +com.example.habits9.data.NumericalHabitType  Int +com.example.habits9.data.NumericalHabitType  NumericalHabitType +com.example.habits9.data.NumericalHabitType  fromInt +com.example.habits9.data.NumericalHabitType  value +com.example.habits9.data.NumericalHabitType  AT_LEAST 5com.example.habits9.data.NumericalHabitType.Companion  AT_MOST 5com.example.habits9.data.NumericalHabitType.Companion  IllegalStateException 5com.example.habits9.data.NumericalHabitType.Companion  fromInt 5com.example.habits9.data.NumericalHabitType.Companion  ApplicationContext 2com.example.habits9.data.UserPreferencesRepository  Context 2com.example.habits9.data.UserPreferencesRepository  Flow 2com.example.habits9.data.UserPreferencesRepository  Inject 2com.example.habits9.data.UserPreferencesRepository  PreferencesKeys 2com.example.habits9.data.UserPreferencesRepository  String 2com.example.habits9.data.UserPreferencesRepository  context 2com.example.habits9.data.UserPreferencesRepository  	dataStore 2com.example.habits9.data.UserPreferencesRepository  edit 2com.example.habits9.data.UserPreferencesRepository  firstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  map 2com.example.habits9.data.UserPreferencesRepository  stringPreferencesKey 2com.example.habits9.data.UserPreferencesRepository  updateFirstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  FIRST_DAY_OF_WEEK Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  stringPreferencesKey Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  ApplicationContext com.example.habits9.di  
CompletionDao com.example.habits9.di  CompletionRepository com.example.habits9.di  Context com.example.habits9.di  DatabaseModule com.example.habits9.di  HabitDao com.example.habits9.di  
HabitDatabase com.example.habits9.di  HabitRepository com.example.habits9.di  HabitSectionDao com.example.habits9.di  HabitSectionRepository com.example.habits9.di  	InstallIn com.example.habits9.di  	Migration com.example.habits9.di  Module com.example.habits9.di  Provides com.example.habits9.di  Room com.example.habits9.di  	Singleton com.example.habits9.di  SingletonComponent com.example.habits9.di  SupportSQLiteDatabase com.example.habits9.di  databaseBuilder com.example.habits9.di  java com.example.habits9.di  
trimIndent com.example.habits9.di  CompletionRepository %com.example.habits9.di.DatabaseModule  
HabitDatabase %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  
MIGRATION_3_4 %com.example.habits9.di.DatabaseModule  
MIGRATION_4_5 %com.example.habits9.di.DatabaseModule  Room %com.example.habits9.di.DatabaseModule  databaseBuilder %com.example.habits9.di.DatabaseModule  java %com.example.habits9.di.DatabaseModule  
trimIndent %com.example.habits9.di.DatabaseModule  Boolean com.example.habits9.ui  
Completion com.example.habits9.ui  CompletionRepository com.example.habits9.ui  DateTimeFormatter com.example.habits9.ui  	DayOfWeek com.example.habits9.ui  	Exception com.example.habits9.ui  Float com.example.habits9.ui  Habit com.example.habits9.ui  HabitRepository com.example.habits9.ui  HabitWithCompletions com.example.habits9.ui  
HiltViewModel com.example.habits9.ui  Inject com.example.habits9.ui  Int com.example.habits9.ui  List com.example.habits9.ui  	LocalDate com.example.habits9.ui  Long com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  Map com.example.habits9.ui  MutableStateFlow com.example.habits9.ui  Pair com.example.habits9.ui  	StateFlow com.example.habits9.ui  String com.example.habits9.ui  TemporalAdjusters com.example.habits9.ui  	ViewModel com.example.habits9.ui  WeekInfo com.example.habits9.ui  WhileSubscribed com.example.habits9.ui  ZoneId com.example.habits9.ui  _completionsState com.example.habits9.ui  	associate com.example.habits9.ui  average com.example.habits9.ui  com com.example.habits9.ui  combine com.example.habits9.ui  completionRepository com.example.habits9.ui  
component1 com.example.habits9.ui  
component2 com.example.habits9.ui  count com.example.habits9.ui  	emptyList com.example.habits9.ui  emptyMap com.example.habits9.ui  getCurrentWeekEnd com.example.habits9.ui  getCurrentWeekStart com.example.habits9.ui  groupBy com.example.habits9.ui  habitRepository com.example.habits9.ui  
isNotEmpty com.example.habits9.ui  java com.example.habits9.ui  kotlinx com.example.habits9.ui  last com.example.habits9.ui  launch com.example.habits9.ui  map com.example.habits9.ui  	mapValues com.example.habits9.ui  reversed com.example.habits9.ui  stateIn com.example.habits9.ui  to com.example.habits9.ui  	uppercase com.example.habits9.ui  userPreferencesRepository com.example.habits9.ui  viewModelScope com.example.habits9.ui  completions +com.example.habits9.ui.HabitWithCompletions  copy +com.example.habits9.ui.HabitWithCompletions  
currentStreak +com.example.habits9.ui.HabitWithCompletions  habit +com.example.habits9.ui.HabitWithCompletions  copy "com.example.habits9.ui.MainUiState  habitsWithCompletions "com.example.habits9.ui.MainUiState  	isLoading "com.example.habits9.ui.MainUiState  weekInfo "com.example.habits9.ui.MainUiState  
Completion $com.example.habits9.ui.MainViewModel  DateTimeFormatter $com.example.habits9.ui.MainViewModel  	DayOfWeek $com.example.habits9.ui.MainViewModel  HabitWithCompletions $com.example.habits9.ui.MainViewModel  	LocalDate $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  MutableStateFlow $com.example.habits9.ui.MainViewModel  Pair $com.example.habits9.ui.MainViewModel  TemporalAdjusters $com.example.habits9.ui.MainViewModel  WeekInfo $com.example.habits9.ui.MainViewModel  WhileSubscribed $com.example.habits9.ui.MainViewModel  ZoneId $com.example.habits9.ui.MainViewModel  _completionsState $com.example.habits9.ui.MainViewModel  	associate $com.example.habits9.ui.MainViewModel  average $com.example.habits9.ui.MainViewModel  calculateCurrentStreak $com.example.habits9.ui.MainViewModel  #calculateDailyCompletionPercentages $com.example.habits9.ui.MainViewModel  calculateWeekInfo $com.example.habits9.ui.MainViewModel  combine $com.example.habits9.ui.MainViewModel  completionRepository $com.example.habits9.ui.MainViewModel  
component1 $com.example.habits9.ui.MainViewModel  
component2 $com.example.habits9.ui.MainViewModel  count $com.example.habits9.ui.MainViewModel  	emptyList $com.example.habits9.ui.MainViewModel  emptyMap $com.example.habits9.ui.MainViewModel  enhancedUiState $com.example.habits9.ui.MainViewModel  getCurrentWeekEnd $com.example.habits9.ui.MainViewModel  getCurrentWeekStart $com.example.habits9.ui.MainViewModel  groupBy $com.example.habits9.ui.MainViewModel  habitRepository $com.example.habits9.ui.MainViewModel  
isNotEmpty $com.example.habits9.ui.MainViewModel  java $com.example.habits9.ui.MainViewModel  kotlinx $com.example.habits9.ui.MainViewModel  last $com.example.habits9.ui.MainViewModel  launch $com.example.habits9.ui.MainViewModel  map $com.example.habits9.ui.MainViewModel  	mapValues $com.example.habits9.ui.MainViewModel  reversed $com.example.habits9.ui.MainViewModel  stateIn $com.example.habits9.ui.MainViewModel  to $com.example.habits9.ui.MainViewModel  toggleCompletion $com.example.habits9.ui.MainViewModel  uiState $com.example.habits9.ui.MainViewModel  	uppercase $com.example.habits9.ui.MainViewModel  userPreferencesRepository $com.example.habits9.ui.MainViewModel  viewModelScope $com.example.habits9.ui.MainViewModel  dailyCompletionPercentages com.example.habits9.ui.WeekInfo  dates com.example.habits9.ui.WeekInfo  formattedDates com.example.habits9.ui.WeekInfo  
timestamps com.example.habits9.ui.WeekInfo  
weekNumber com.example.habits9.ui.WeekInfo  weeklyCompletionPercentage com.example.habits9.ui.WeekInfo  example com.example.habits9.ui.com  habits9 "com.example.habits9.ui.com.example  data *com.example.habits9.ui.com.example.habits9  UserPreferencesRepository /com.example.habits9.ui.com.example.habits9.data  Boolean "com.example.habits9.ui.createhabit  CreateHabitUiState "com.example.habits9.ui.createhabit  CreateHabitViewModel "com.example.habits9.ui.createhabit  	DayOfWeek "com.example.habits9.ui.createhabit  	Exception "com.example.habits9.ui.createhabit  HabitFrequency "com.example.habits9.ui.createhabit  HabitSection "com.example.habits9.ui.createhabit  HabitSectionRepository "com.example.habits9.ui.createhabit  
HiltViewModel "com.example.habits9.ui.createhabit  Inject "com.example.habits9.ui.createhabit  Int "com.example.habits9.ui.createhabit  List "com.example.habits9.ui.createhabit  	LocalTime "com.example.habits9.ui.createhabit  MutableStateFlow "com.example.habits9.ui.createhabit  
ReminderState "com.example.habits9.ui.createhabit  Set "com.example.habits9.ui.createhabit  	StateFlow "com.example.habits9.ui.createhabit  String "com.example.habits9.ui.createhabit  Unit "com.example.habits9.ui.createhabit  	ViewModel "com.example.habits9.ui.createhabit  _uiState "com.example.habits9.ui.createhabit  asStateFlow "com.example.habits9.ui.createhabit  com "com.example.habits9.ui.createhabit  	emptyList "com.example.habits9.ui.createhabit  first "com.example.habits9.ui.createhabit  format "com.example.habits9.ui.createhabit  habitRepository "com.example.habits9.ui.createhabit  habitSectionRepository "com.example.habits9.ui.createhabit  isBlank "com.example.habits9.ui.createhabit  
isNotEmpty "com.example.habits9.ui.createhabit  joinToString "com.example.habits9.ui.createhabit  launch "com.example.habits9.ui.createhabit  	lowercase "com.example.habits9.ui.createhabit  map "com.example.habits9.ui.createhabit  replaceFirstChar "com.example.habits9.ui.createhabit  setOf "com.example.habits9.ui.createhabit  sortedBy "com.example.habits9.ui.createhabit  toDoubleOrNull "com.example.habits9.ui.createhabit  	uppercase "com.example.habits9.ui.createhabit  availableSections 5com.example.habits9.ui.createhabit.CreateHabitUiState  copy 5com.example.habits9.ui.createhabit.CreateHabitUiState  
reminderState 5com.example.habits9.ui.createhabit.CreateHabitUiState  
selectedColor 5com.example.habits9.ui.createhabit.CreateHabitUiState  selectedFrequency 5com.example.habits9.ui.createhabit.CreateHabitUiState  selectedSection 5com.example.habits9.ui.createhabit.CreateHabitUiState  showReminderDialog 5com.example.habits9.ui.createhabit.CreateHabitUiState  showSectionSelector 5com.example.habits9.ui.createhabit.CreateHabitUiState  CreateHabitUiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  MutableStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  _uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  asStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  com 7com.example.habits9.ui.createhabit.CreateHabitViewModel  first 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitSectionRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  isBlank 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
isNotEmpty 7com.example.habits9.ui.createhabit.CreateHabitViewModel  launch 7com.example.habits9.ui.createhabit.CreateHabitViewModel  loadSections 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	saveHabit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
selectSection 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  toDoubleOrNull 7com.example.habits9.ui.createhabit.CreateHabitViewModel  uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateReminder 7com.example.habits9.ui.createhabit.CreateHabitViewModel  viewModelScope 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	Companion 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY 1com.example.habits9.ui.createhabit.HabitFrequency  HabitFrequency 1com.example.habits9.ui.createhabit.HabitFrequency  Int 1com.example.habits9.ui.createhabit.HabitFrequency  MONTHLY 1com.example.habits9.ui.createhabit.HabitFrequency  String 1com.example.habits9.ui.createhabit.HabitFrequency  WEEKLY 1com.example.habits9.ui.createhabit.HabitFrequency  denominator 1com.example.habits9.ui.createhabit.HabitFrequency  	numerator 1com.example.habits9.ui.createhabit.HabitFrequency  toDisplayString 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  HabitFrequency ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  MONTHLY ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  WEEKLY ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  	DayOfWeek 0com.example.habits9.ui.createhabit.ReminderState  String 0com.example.habits9.ui.createhabit.ReminderState  first 0com.example.habits9.ui.createhabit.ReminderState  format 0com.example.habits9.ui.createhabit.ReminderState  	isEnabled 0com.example.habits9.ui.createhabit.ReminderState  joinToString 0com.example.habits9.ui.createhabit.ReminderState  	lowercase 0com.example.habits9.ui.createhabit.ReminderState  map 0com.example.habits9.ui.createhabit.ReminderState  replaceFirstChar 0com.example.habits9.ui.createhabit.ReminderState  selectedDays 0com.example.habits9.ui.createhabit.ReminderState  sortedBy 0com.example.habits9.ui.createhabit.ReminderState  time 0com.example.habits9.ui.createhabit.ReminderState  toDisplayString 0com.example.habits9.ui.createhabit.ReminderState  	uppercase 0com.example.habits9.ui.createhabit.ReminderState  example &com.example.habits9.ui.createhabit.com  habits9 .com.example.habits9.ui.createhabit.com.example  data 6com.example.habits9.ui.createhabit.com.example.habits9  HabitRepository ;com.example.habits9.ui.createhabit.com.example.habits9.data  	HabitType ;com.example.habits9.ui.createhabit.com.example.habits9.data  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  AlertDialog ,com.example.habits9.ui.createmeasurablehabit  	Alignment ,com.example.habits9.ui.createmeasurablehabit  Arrangement ,com.example.habits9.ui.createmeasurablehabit  Box ,com.example.habits9.ui.createmeasurablehabit  CircleShape ,com.example.habits9.ui.createmeasurablehabit  Color ,com.example.habits9.ui.createmeasurablehabit  Column ,com.example.habits9.ui.createmeasurablehabit  
Composable ,com.example.habits9.ui.createmeasurablehabit  CreateHabitViewModel ,com.example.habits9.ui.createmeasurablehabit  CreateMeasurableHabitScreen ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  	DayOfWeek ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  DropdownMenuItem ,com.example.habits9.ui.createmeasurablehabit  ExperimentalMaterial3Api ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuBox ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuDefaults ,com.example.habits9.ui.createmeasurablehabit  
FontWeight ,com.example.habits9.ui.createmeasurablehabit  HabitFrequency ,com.example.habits9.ui.createmeasurablehabit  Icon ,com.example.habits9.ui.createmeasurablehabit  
IconButton ,com.example.habits9.ui.createmeasurablehabit  Icons ,com.example.habits9.ui.createmeasurablehabit  LaunchedEffect ,com.example.habits9.ui.createmeasurablehabit  List ,com.example.habits9.ui.createmeasurablehabit  	LocalTime ,com.example.habits9.ui.createmeasurablehabit  Modifier ,com.example.habits9.ui.createmeasurablehabit  OptIn ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextField ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextFieldDefaults ,com.example.habits9.ui.createmeasurablehabit  ReminderDialog ,com.example.habits9.ui.createmeasurablehabit  
ReminderState ,com.example.habits9.ui.createmeasurablehabit  Row ,com.example.habits9.ui.createmeasurablehabit  Scaffold ,com.example.habits9.ui.createmeasurablehabit  SectionSelectorDialog ,com.example.habits9.ui.createmeasurablehabit  Spacer ,com.example.habits9.ui.createmeasurablehabit  String ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  Switch ,com.example.habits9.ui.createmeasurablehabit  SwitchDefaults ,com.example.habits9.ui.createmeasurablehabit  Text ,com.example.habits9.ui.createmeasurablehabit  
TextButton ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  
TimePicker ,com.example.habits9.ui.createmeasurablehabit  TimePickerDefaults ,com.example.habits9.ui.createmeasurablehabit  TimePickerDialog ,com.example.habits9.ui.createmeasurablehabit  	TopAppBar ,com.example.habits9.ui.createmeasurablehabit  TopAppBarDefaults ,com.example.habits9.ui.createmeasurablehabit  TrailingIcon ,com.example.habits9.ui.createmeasurablehabit  Unit ,com.example.habits9.ui.createmeasurablehabit  
background ,com.example.habits9.ui.createmeasurablehabit  	clickable ,com.example.habits9.ui.createmeasurablehabit  collectAsState ,com.example.habits9.ui.createmeasurablehabit  colors ,com.example.habits9.ui.createmeasurablehabit  com ,com.example.habits9.ui.createmeasurablehabit  fillMaxSize ,com.example.habits9.ui.createmeasurablehabit  fillMaxWidth ,com.example.habits9.ui.createmeasurablehabit  forEach ,com.example.habits9.ui.createmeasurablehabit  format ,com.example.habits9.ui.createmeasurablehabit  getValue ,com.example.habits9.ui.createmeasurablehabit  height ,com.example.habits9.ui.createmeasurablehabit  
isNotEmpty ,com.example.habits9.ui.createmeasurablehabit  kotlinx ,com.example.habits9.ui.createmeasurablehabit  listOf ,com.example.habits9.ui.createmeasurablehabit  minus ,com.example.habits9.ui.createmeasurablehabit  mutableStateOf ,com.example.habits9.ui.createmeasurablehabit  padding ,com.example.habits9.ui.createmeasurablehabit  plus ,com.example.habits9.ui.createmeasurablehabit  provideDelegate ,com.example.habits9.ui.createmeasurablehabit  remember ,com.example.habits9.ui.createmeasurablehabit  rememberTimePickerState ,com.example.habits9.ui.createmeasurablehabit  setValue ,com.example.habits9.ui.createmeasurablehabit  size ,com.example.habits9.ui.createmeasurablehabit  to ,com.example.habits9.ui.createmeasurablehabit  topAppBarColors ,com.example.habits9.ui.createmeasurablehabit  weight ,com.example.habits9.ui.createmeasurablehabit  width ,com.example.habits9.ui.createmeasurablehabit  example 0com.example.habits9.ui.createmeasurablehabit.com  habits9 8com.example.habits9.ui.createmeasurablehabit.com.example  data @com.example.habits9.ui.createmeasurablehabit.com.example.habits9  HabitSection Ecom.example.habits9.ui.createmeasurablehabit.com.example.habits9.data  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  AlertDialog 'com.example.habits9.ui.createyesnohabit  	Alignment 'com.example.habits9.ui.createyesnohabit  Arrangement 'com.example.habits9.ui.createyesnohabit  Boolean 'com.example.habits9.ui.createyesnohabit  Box 'com.example.habits9.ui.createyesnohabit  CircleShape 'com.example.habits9.ui.createyesnohabit  Color 'com.example.habits9.ui.createyesnohabit  Column 'com.example.habits9.ui.createyesnohabit  
Composable 'com.example.habits9.ui.createyesnohabit  CreateHabitViewModel 'com.example.habits9.ui.createyesnohabit  CreateYesNoHabitScreen 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  	DayOfWeek 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  ExperimentalMaterial3Api 'com.example.habits9.ui.createyesnohabit  
FontWeight 'com.example.habits9.ui.createyesnohabit  FrequencyOption 'com.example.habits9.ui.createyesnohabit  FrequencyPickerDialog 'com.example.habits9.ui.createyesnohabit  FrequencyRadioOption 'com.example.habits9.ui.createyesnohabit  HabitFrequency 'com.example.habits9.ui.createyesnohabit  Icon 'com.example.habits9.ui.createyesnohabit  
IconButton 'com.example.habits9.ui.createyesnohabit  Icons 'com.example.habits9.ui.createyesnohabit  LaunchedEffect 'com.example.habits9.ui.createyesnohabit  List 'com.example.habits9.ui.createyesnohabit  	LocalTime 'com.example.habits9.ui.createyesnohabit  Modifier 'com.example.habits9.ui.createyesnohabit  OptIn 'com.example.habits9.ui.createyesnohabit  OutlinedTextField 'com.example.habits9.ui.createyesnohabit  OutlinedTextFieldDefaults 'com.example.habits9.ui.createyesnohabit  RadioButton 'com.example.habits9.ui.createyesnohabit  RadioButtonDefaults 'com.example.habits9.ui.createyesnohabit  ReminderDialog 'com.example.habits9.ui.createyesnohabit  
ReminderState 'com.example.habits9.ui.createyesnohabit  Row 'com.example.habits9.ui.createyesnohabit  Scaffold 'com.example.habits9.ui.createyesnohabit  SectionSelectorDialog 'com.example.habits9.ui.createyesnohabit  Spacer 'com.example.habits9.ui.createyesnohabit  String 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  Switch 'com.example.habits9.ui.createyesnohabit  SwitchDefaults 'com.example.habits9.ui.createyesnohabit  Text 'com.example.habits9.ui.createyesnohabit  
TextButton 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  
TimePicker 'com.example.habits9.ui.createyesnohabit  TimePickerDefaults 'com.example.habits9.ui.createyesnohabit  TimePickerDialog 'com.example.habits9.ui.createyesnohabit  	TopAppBar 'com.example.habits9.ui.createyesnohabit  TopAppBarDefaults 'com.example.habits9.ui.createyesnohabit  Unit 'com.example.habits9.ui.createyesnohabit  
background 'com.example.habits9.ui.createyesnohabit  	clickable 'com.example.habits9.ui.createyesnohabit  collectAsState 'com.example.habits9.ui.createyesnohabit  colors 'com.example.habits9.ui.createyesnohabit  com 'com.example.habits9.ui.createyesnohabit  fillMaxSize 'com.example.habits9.ui.createyesnohabit  fillMaxWidth 'com.example.habits9.ui.createyesnohabit  forEach 'com.example.habits9.ui.createyesnohabit  format 'com.example.habits9.ui.createyesnohabit  getSelectedOption 'com.example.habits9.ui.createyesnohabit  getValue 'com.example.habits9.ui.createyesnohabit  height 'com.example.habits9.ui.createyesnohabit  
isNotBlank 'com.example.habits9.ui.createyesnohabit  
isNotEmpty 'com.example.habits9.ui.createyesnohabit  kotlinx 'com.example.habits9.ui.createyesnohabit  listOf 'com.example.habits9.ui.createyesnohabit  minus 'com.example.habits9.ui.createyesnohabit  mutableStateOf 'com.example.habits9.ui.createyesnohabit  padding 'com.example.habits9.ui.createyesnohabit  plus 'com.example.habits9.ui.createyesnohabit  provideDelegate 'com.example.habits9.ui.createyesnohabit  remember 'com.example.habits9.ui.createyesnohabit  rememberTimePickerState 'com.example.habits9.ui.createyesnohabit  setValue 'com.example.habits9.ui.createyesnohabit  size 'com.example.habits9.ui.createyesnohabit  to 'com.example.habits9.ui.createyesnohabit  toInt 'com.example.habits9.ui.createyesnohabit  toIntOrNull 'com.example.habits9.ui.createyesnohabit  topAppBarColors 'com.example.habits9.ui.createyesnohabit  weight 'com.example.habits9.ui.createyesnohabit  width 'com.example.habits9.ui.createyesnohabit  	EVERY_DAY 7com.example.habits9.ui.createyesnohabit.FrequencyOption  EVERY_X_DAYS 7com.example.habits9.ui.createyesnohabit.FrequencyOption  X_TIMES_PER_MONTH 7com.example.habits9.ui.createyesnohabit.FrequencyOption  X_TIMES_PER_WEEK 7com.example.habits9.ui.createyesnohabit.FrequencyOption  example +com.example.habits9.ui.createyesnohabit.com  habits9 3com.example.habits9.ui.createyesnohabit.com.example  data ;com.example.habits9.ui.createyesnohabit.com.example.habits9  HabitSection @com.example.habits9.ui.createyesnohabit.com.example.habits9.data  
AccentPrimary com.example.habits9.ui.details  	Alignment com.example.habits9.ui.details  Arrangement com.example.habits9.ui.details  Build com.example.habits9.ui.details  CircularProgressIndicator com.example.habits9.ui.details  
Composable com.example.habits9.ui.details  DarkBackground com.example.habits9.ui.details  DividerColor com.example.habits9.ui.details  ExperimentalMaterial3Api com.example.habits9.ui.details  Float com.example.habits9.ui.details  
FontFamily com.example.habits9.ui.details  
FontWeight com.example.habits9.ui.details  HabitDetailsScreen com.example.habits9.ui.details  Icon com.example.habits9.ui.details  
IconButton com.example.habits9.ui.details  Icons com.example.habits9.ui.details  
MetricView com.example.habits9.ui.details  Modifier com.example.habits9.ui.details  OptIn com.example.habits9.ui.details  OverviewSection com.example.habits9.ui.details  RequiresApi com.example.habits9.ui.details  Row com.example.habits9.ui.details  Spacer com.example.habits9.ui.details  String com.example.habits9.ui.details  SubHeaderRow com.example.habits9.ui.details  SurfaceVariantDark com.example.habits9.ui.details  Text com.example.habits9.ui.details  TextPrimary com.example.habits9.ui.details  
TextSecondary com.example.habits9.ui.details  TopAppBarDefaults com.example.habits9.ui.details  Unit com.example.habits9.ui.details  fillMaxSize com.example.habits9.ui.details  fillMaxWidth com.example.habits9.ui.details  height com.example.habits9.ui.details  size com.example.habits9.ui.details  topAppBarColors com.example.habits9.ui.details  width com.example.habits9.ui.details  
AccentPrimary )com.example.habits9.ui.habittypeselection  	Alignment )com.example.habits9.ui.habittypeselection  Arrangement )com.example.habits9.ui.habittypeselection  Card )com.example.habits9.ui.habittypeselection  CardDefaults )com.example.habits9.ui.habittypeselection  Column )com.example.habits9.ui.habittypeselection  
Composable )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  ExperimentalMaterial3Api )com.example.habits9.ui.habittypeselection  
FontWeight )com.example.habits9.ui.habittypeselection  HabitTypeSelectionScreen )com.example.habits9.ui.habittypeselection  Icon )com.example.habits9.ui.habittypeselection  
IconButton )com.example.habits9.ui.habittypeselection  Icons )com.example.habits9.ui.habittypeselection  Modifier )com.example.habits9.ui.habittypeselection  OptIn )com.example.habits9.ui.habittypeselection  RoundedCornerShape )com.example.habits9.ui.habittypeselection  Scaffold )com.example.habits9.ui.habittypeselection  Spacer )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  Text )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  	TopAppBar )com.example.habits9.ui.habittypeselection  TopAppBarDefaults )com.example.habits9.ui.habittypeselection  Unit )com.example.habits9.ui.habittypeselection  
cardColors )com.example.habits9.ui.habittypeselection  	clickable )com.example.habits9.ui.habittypeselection  fillMaxSize )com.example.habits9.ui.habittypeselection  fillMaxWidth )com.example.habits9.ui.habittypeselection  height )com.example.habits9.ui.habittypeselection  padding )com.example.habits9.ui.habittypeselection  topAppBarColors )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  	Alignment com.example.habits9.ui.home  Arrangement com.example.habits9.ui.home  BackgroundDark com.example.habits9.ui.home  Boolean com.example.habits9.ui.home  Box com.example.habits9.ui.home  Build com.example.habits9.ui.home  CircleShape com.example.habits9.ui.home  Color com.example.habits9.ui.home  Column com.example.habits9.ui.home  CompletionIndicator com.example.habits9.ui.home  
Composable com.example.habits9.ui.home  CustomHeader com.example.habits9.ui.home  	DayOfWeek com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  DropdownMenu com.example.habits9.ui.home  DropdownMenuItem com.example.habits9.ui.home  ExperimentalMaterial3Api com.example.habits9.ui.home  
FontFamily com.example.habits9.ui.home  
FontWeight com.example.habits9.ui.home  FrozenPaneLayout com.example.habits9.ui.home  
HomeScreen com.example.habits9.ui.home  Icon com.example.habits9.ui.home  
IconButton com.example.habits9.ui.home  Icons com.example.habits9.ui.home  
LazyColumn com.example.habits9.ui.home  List com.example.habits9.ui.home  	LocalDate com.example.habits9.ui.home  Long com.example.habits9.ui.home  Modifier com.example.habits9.ui.home  Offset com.example.habits9.ui.home  OptIn com.example.habits9.ui.home  RequiresApi com.example.habits9.ui.home  Row com.example.habits9.ui.home  Spacer com.example.habits9.ui.home  Stroke com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  Text com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  TopAppBarDefaults com.example.habits9.ui.home  Unit com.example.habits9.ui.home  
background com.example.habits9.ui.home  	clickable com.example.habits9.ui.home  com com.example.habits9.ui.home  
drawBehind com.example.habits9.ui.home  fillMaxSize com.example.habits9.ui.home  fillMaxWidth com.example.habits9.ui.home  forEachIndexed com.example.habits9.ui.home  height com.example.habits9.ui.home  horizontalScroll com.example.habits9.ui.home  isWeekStartDay com.example.habits9.ui.home  padding com.example.habits9.ui.home  provideDelegate com.example.habits9.ui.home  size com.example.habits9.ui.home  spacedBy com.example.habits9.ui.home  split com.example.habits9.ui.home  topAppBarColors com.example.habits9.ui.home  weight com.example.habits9.ui.home  width com.example.habits9.ui.home  example com.example.habits9.ui.home.com  habits9 'com.example.habits9.ui.home.com.example  ui /com.example.habits9.ui.home.com.example.habits9  HabitWithCompletions 2com.example.habits9.ui.home.com.example.habits9.ui  
MainViewModel 2com.example.habits9.ui.home.com.example.habits9.ui  WeekInfo 2com.example.habits9.ui.home.com.example.habits9.ui  
AccentPrimary %com.example.habits9.ui.managesections  AlertDialog %com.example.habits9.ui.managesections  	Alignment %com.example.habits9.ui.managesections  Arrangement %com.example.habits9.ui.managesections  Boolean %com.example.habits9.ui.managesections  Box %com.example.habits9.ui.managesections  Card %com.example.habits9.ui.managesections  CardDefaults %com.example.habits9.ui.managesections  CircleShape %com.example.habits9.ui.managesections  CircularProgressIndicator %com.example.habits9.ui.managesections  Color %com.example.habits9.ui.managesections  Column %com.example.habits9.ui.managesections  
Composable %com.example.habits9.ui.managesections  CreateSectionDialog %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DeleteSectionDialog %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  EditSectionDialog %com.example.habits9.ui.managesections  ExperimentalMaterial3Api %com.example.habits9.ui.managesections  
FontWeight %com.example.habits9.ui.managesections  GhostPlaceholder %com.example.habits9.ui.managesections  HabitSection %com.example.habits9.ui.managesections  HabitSectionRepository %com.example.habits9.ui.managesections  HapticFeedbackType %com.example.habits9.ui.managesections  
HiltViewModel %com.example.habits9.ui.managesections  Icon %com.example.habits9.ui.managesections  
IconButton %com.example.habits9.ui.managesections  Icons %com.example.habits9.ui.managesections  Inject %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  
LazyColumn %com.example.habits9.ui.managesections  LazyRow %com.example.habits9.ui.managesections  List %com.example.habits9.ui.managesections  ManageSectionsScreen %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  Modifier %com.example.habits9.ui.managesections  MutableStateFlow %com.example.habits9.ui.managesections  Offset %com.example.habits9.ui.managesections  OptIn %com.example.habits9.ui.managesections  OutlinedTextField %com.example.habits9.ui.managesections  OutlinedTextFieldDefaults %com.example.habits9.ui.managesections  
PaddingValues %com.example.habits9.ui.managesections  RoundedCornerShape %com.example.habits9.ui.managesections  Row %com.example.habits9.ui.managesections  Scaffold %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  SectionListItem %com.example.habits9.ui.managesections  Spacer %com.example.habits9.ui.managesections  	StateFlow %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  Text %com.example.habits9.ui.managesections  
TextButton %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  	TopAppBar %com.example.habits9.ui.managesections  TopAppBarDefaults %com.example.habits9.ui.managesections  Unit %com.example.habits9.ui.managesections  	ViewModel %com.example.habits9.ui.managesections  _uiState %com.example.habits9.ui.managesections  align %com.example.habits9.ui.managesections  alpha %com.example.habits9.ui.managesections  animateContentSize %com.example.habits9.ui.managesections  asStateFlow %com.example.habits9.ui.managesections  
background %com.example.habits9.ui.managesections  
cardColors %com.example.habits9.ui.managesections  	clickable %com.example.habits9.ui.managesections  coerceAtMost %com.example.habits9.ui.managesections  collectAsState %com.example.habits9.ui.managesections  colors %com.example.habits9.ui.managesections  	emptyList %com.example.habits9.ui.managesections  fillMaxSize %com.example.habits9.ui.managesections  fillMaxWidth %com.example.habits9.ui.managesections  find %com.example.habits9.ui.managesections  forEachIndexed %com.example.habits9.ui.managesections  getValue %com.example.habits9.ui.managesections  
graphicsLayer %com.example.habits9.ui.managesections  habitSectionRepository %com.example.habits9.ui.managesections  height %com.example.habits9.ui.managesections  hideCreateDialog %com.example.habits9.ui.managesections  hideDeleteDialog %com.example.habits9.ui.managesections  hideEditDialog %com.example.habits9.ui.managesections  indexOfFirst %com.example.habits9.ui.managesections  isBlank %com.example.habits9.ui.managesections  
isNotBlank %com.example.habits9.ui.managesections  launch %com.example.habits9.ui.managesections  let %com.example.habits9.ui.managesections  listOf %com.example.habits9.ui.managesections  
mapIndexed %com.example.habits9.ui.managesections  mutableStateOf %com.example.habits9.ui.managesections  padding %com.example.habits9.ui.managesections  
plusAssign %com.example.habits9.ui.managesections  pointerInput %com.example.habits9.ui.managesections  provideDelegate %com.example.habits9.ui.managesections  remember %com.example.habits9.ui.managesections  rememberCoroutineScope %com.example.habits9.ui.managesections  setValue %com.example.habits9.ui.managesections  shadow %com.example.habits9.ui.managesections  size %com.example.habits9.ui.managesections  spacedBy %com.example.habits9.ui.managesections  takeIf %com.example.habits9.ui.managesections  
toMutableList %com.example.habits9.ui.managesections  topAppBarColors %com.example.habits9.ui.managesections  trim %com.example.habits9.ui.managesections  weight %com.example.habits9.ui.managesections  width %com.example.habits9.ui.managesections  zIndex %com.example.habits9.ui.managesections  copy ;com.example.habits9.ui.managesections.ManageSectionsUiState  deletingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  editingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  	isLoading ;com.example.habits9.ui.managesections.ManageSectionsUiState  sections ;com.example.habits9.ui.managesections.ManageSectionsUiState  showCreateDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showDeleteDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showEditDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  MutableStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  _uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  asStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  
createSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  
deleteSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  habitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  isBlank =com.example.habits9.ui.managesections.ManageSectionsViewModel  launch =com.example.habits9.ui.managesections.ManageSectionsViewModel  loadSections =com.example.habits9.ui.managesections.ManageSectionsViewModel  
mapIndexed =com.example.habits9.ui.managesections.ManageSectionsViewModel  onSectionMoved =com.example.habits9.ui.managesections.ManageSectionsViewModel  showCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  
toMutableList =com.example.habits9.ui.managesections.ManageSectionsViewModel  trim =com.example.habits9.ui.managesections.ManageSectionsViewModel  uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  
updateSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  viewModelScope =com.example.habits9.ui.managesections.ManageSectionsViewModel  
AccentPrimary com.example.habits9.ui.settings  	Alignment com.example.habits9.ui.settings  Column com.example.habits9.ui.settings  
Composable com.example.habits9.ui.settings  DarkBackground com.example.habits9.ui.settings  ExperimentalMaterial3Api com.example.habits9.ui.settings  
FontFamily com.example.habits9.ui.settings  
FontWeight com.example.habits9.ui.settings  
HiltViewModel com.example.habits9.ui.settings  Icons com.example.habits9.ui.settings  Inject com.example.habits9.ui.settings  Modifier com.example.habits9.ui.settings  OptIn com.example.habits9.ui.settings  RadioButton com.example.habits9.ui.settings  RadioButtonDefaults com.example.habits9.ui.settings  Role com.example.habits9.ui.settings  Row com.example.habits9.ui.settings  SettingsScreen com.example.habits9.ui.settings  SettingsViewModel com.example.habits9.ui.settings  SharingStarted com.example.habits9.ui.settings  Spacer com.example.habits9.ui.settings  	StateFlow com.example.habits9.ui.settings  String com.example.habits9.ui.settings  SurfaceVariantDark com.example.habits9.ui.settings  Text com.example.habits9.ui.settings  TextPrimary com.example.habits9.ui.settings  
TextSecondary com.example.habits9.ui.settings  TopAppBarDefaults com.example.habits9.ui.settings  Unit com.example.habits9.ui.settings  UserPreferencesRepository com.example.habits9.ui.settings  	ViewModel com.example.habits9.ui.settings  WhileSubscribed com.example.habits9.ui.settings  colors com.example.habits9.ui.settings  fillMaxSize com.example.habits9.ui.settings  fillMaxWidth com.example.habits9.ui.settings  height com.example.habits9.ui.settings  launch com.example.habits9.ui.settings  padding com.example.habits9.ui.settings  provideDelegate com.example.habits9.ui.settings  
selectable com.example.habits9.ui.settings  selectableGroup com.example.habits9.ui.settings  stateIn com.example.habits9.ui.settings  topAppBarColors com.example.habits9.ui.settings  userPreferencesRepository com.example.habits9.ui.settings  width com.example.habits9.ui.settings  SharingStarted 1com.example.habits9.ui.settings.SettingsViewModel  WhileSubscribed 1com.example.habits9.ui.settings.SettingsViewModel  firstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  launch 1com.example.habits9.ui.settings.SettingsViewModel  stateIn 1com.example.habits9.ui.settings.SettingsViewModel  updateFirstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  userPreferencesRepository 1com.example.habits9.ui.settings.SettingsViewModel  viewModelScope 1com.example.habits9.ui.settings.SettingsViewModel  AndroidEntryPoint com.example.uhabits_99  Build com.example.uhabits_99  Bundle com.example.uhabits_99  ComponentActivity com.example.uhabits_99  CreateMeasurableHabitScreen com.example.uhabits_99  CreateYesNoHabitScreen com.example.uhabits_99  HabitDetailsScreen com.example.uhabits_99  HabitTypeSelectionScreen com.example.uhabits_99  
HomeScreen com.example.uhabits_99  MainActivity com.example.uhabits_99  ManageSectionsScreen com.example.uhabits_99  NavHost com.example.uhabits_99  RequiresApi com.example.uhabits_99  SettingsScreen com.example.uhabits_99  UHabits_99Theme com.example.uhabits_99  rememberNavController com.example.uhabits_99  Build #com.example.uhabits_99.MainActivity  CreateMeasurableHabitScreen #com.example.uhabits_99.MainActivity  CreateYesNoHabitScreen #com.example.uhabits_99.MainActivity  HabitDetailsScreen #com.example.uhabits_99.MainActivity  HabitTypeSelectionScreen #com.example.uhabits_99.MainActivity  
HomeScreen #com.example.uhabits_99.MainActivity  ManageSectionsScreen #com.example.uhabits_99.MainActivity  NavHost #com.example.uhabits_99.MainActivity  SettingsScreen #com.example.uhabits_99.MainActivity  UHabits_99Theme #com.example.uhabits_99.MainActivity  
composable #com.example.uhabits_99.MainActivity  rememberNavController #com.example.uhabits_99.MainActivity  
setContent #com.example.uhabits_99.MainActivity  Boolean com.example.uhabits_99.ui.theme  Build com.example.uhabits_99.ui.theme  
Composable com.example.uhabits_99.ui.theme  DarkColorScheme com.example.uhabits_99.ui.theme  
FontFamily com.example.uhabits_99.ui.theme  
FontWeight com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  UHabits_99Theme com.example.uhabits_99.ui.theme  Unit com.example.uhabits_99.ui.theme  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  IllegalStateException 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	DayOfWeek 	java.time  	LocalDate 	java.time  	LocalTime 	java.time  ZoneId 	java.time  FRIDAY java.time.DayOfWeek  MONDAY java.time.DayOfWeek  SATURDAY java.time.DayOfWeek  SUNDAY java.time.DayOfWeek  THURSDAY java.time.DayOfWeek  TUESDAY java.time.DayOfWeek  	WEDNESDAY java.time.DayOfWeek  name java.time.DayOfWeek  to java.time.DayOfWeek  value java.time.DayOfWeek  toEpochMilli java.time.Instant  atStartOfDay java.time.LocalDate  	dayOfWeek java.time.LocalDate  format java.time.LocalDate  get java.time.LocalDate  	minusDays java.time.LocalDate  now java.time.LocalDate  plusDays java.time.LocalDate  with java.time.LocalDate  hour java.time.LocalTime  minute java.time.LocalTime  of java.time.LocalTime  
systemDefault java.time.ZoneId  	toInstant java.time.ZonedDateTime  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  TemporalAdjusters java.time.temporal  previousOrSame $java.time.temporal.TemporalAdjusters  of java.time.temporal.WeekFields  
weekOfYear java.time.temporal.WeekFields  
Comparator 	java.util  UUID 	java.util  
getDefault java.util.Locale  
randomUUID java.util.UUID  toString java.util.UUID  Inject javax.inject  	Singleton javax.inject  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  Enum kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  map kotlin  minus kotlin  plus kotlin  takeIf kotlin  to kotlin  not kotlin.Boolean  	uppercase kotlin.Char  sp 
kotlin.Double  toFloat 
kotlin.Double  AT_LEAST kotlin.Enum  AT_MOST kotlin.Enum  	Companion kotlin.Enum  	HabitType kotlin.Enum  IllegalStateException kotlin.Enum  Int kotlin.Enum  	NUMERICAL kotlin.Enum  NumericalHabitType kotlin.Enum  YES_NO kotlin.Enum  AT_LEAST kotlin.Enum.Companion  AT_MOST kotlin.Enum.Companion  IllegalStateException kotlin.Enum.Companion  	NUMERICAL kotlin.Enum.Companion  YES_NO kotlin.Enum.Companion  message kotlin.Exception  printStackTrace kotlin.Exception  	compareTo kotlin.Float  div kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  coerceAtMost 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  takeIf 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  times kotlin.Long  to kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  format 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  	lowercase 
kotlin.String  replace 
kotlin.String  replaceFirstChar 
kotlin.String  split 
kotlin.String  to 
kotlin.String  toDoubleOrNull 
kotlin.String  toInt 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  	associate kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  find kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  groupBy kotlin.collections  indexOfFirst kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  last kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  	mapValues kotlin.collections  minus kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  reversed kotlin.collections  setOf kotlin.collections  sortedBy kotlin.collections  
toMutableList kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  	associate kotlin.collections.List  average kotlin.collections.List  count kotlin.collections.List  find kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  groupBy kotlin.collections.List  indexOfFirst kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  last kotlin.collections.List  map kotlin.collections.List  reversed kotlin.collections.List  size kotlin.collections.List  
toMutableList kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  	mapValues kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  
mapIndexed kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  contains kotlin.collections.Set  first kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  minus kotlin.collections.Set  plus kotlin.collections.Set  size kotlin.collections.Set  sortedBy kotlin.collections.Set  reversed kotlin.comparisons  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  SuspendFunction3 kotlin.coroutines  java 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  coerceAtMost 
kotlin.ranges  first 
kotlin.ranges  last 
kotlin.ranges  reversed 
kotlin.ranges  contains kotlin.ranges.IntRange  map kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  	associate kotlin.sequences  average kotlin.sequences  count kotlin.sequences  find kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  groupBy kotlin.sequences  indexOfFirst kotlin.sequences  joinToString kotlin.sequences  last kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  minus kotlin.sequences  plus kotlin.sequences  sortedBy kotlin.sequences  
toMutableList kotlin.sequences  	associate kotlin.text  count kotlin.text  find kotlin.text  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  groupBy kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  last kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapIndexed kotlin.text  plus kotlin.text  replace kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  reversed kotlin.text  split kotlin.text  toDoubleOrNull kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  
toMutableList kotlin.text  trim kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  
Completion !kotlinx.coroutines.CoroutineScope  HabitSection !kotlinx.coroutines.CoroutineScope  Pair !kotlinx.coroutines.CoroutineScope  _completionsState !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  	associate !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  combine !kotlinx.coroutines.CoroutineScope  completionRepository !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  emptyMap !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  getCurrentWeekEnd !kotlinx.coroutines.CoroutineScope  getCurrentWeekStart !kotlinx.coroutines.CoroutineScope  groupBy !kotlinx.coroutines.CoroutineScope  habitRepository !kotlinx.coroutines.CoroutineScope  habitSectionRepository !kotlinx.coroutines.CoroutineScope  hideCreateDialog !kotlinx.coroutines.CoroutineScope  hideDeleteDialog !kotlinx.coroutines.CoroutineScope  hideEditDialog !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  	mapValues !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toDoubleOrNull !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  userPreferencesRepository !kotlinx.coroutines.CoroutineScope  viewModelScope !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow  first com.example.habits9.ui  first $com.example.habits9.ui.MainViewModel  formattedDayAbbreviations com.example.habits9.ui.WeekInfo  
JavaDayOfWeek com.example.habits9.ui  WeekBoundaryUtils com.example.habits9.ui  
WeekFields com.example.habits9.ui  getCurrentWeekDates com.example.habits9.ui  
getWeekEnd com.example.habits9.ui  
getWeekNumber com.example.habits9.ui  getWeekStart com.example.habits9.ui  Boolean $com.example.habits9.ui.MainViewModel  CompletionRepository $com.example.habits9.ui.MainViewModel  	Exception $com.example.habits9.ui.MainViewModel  Float $com.example.habits9.ui.MainViewModel  HabitRepository $com.example.habits9.ui.MainViewModel  Inject $com.example.habits9.ui.MainViewModel  Int $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek $com.example.habits9.ui.MainViewModel  List $com.example.habits9.ui.MainViewModel  Long $com.example.habits9.ui.MainViewModel  Map $com.example.habits9.ui.MainViewModel  	StateFlow $com.example.habits9.ui.MainViewModel  String $com.example.habits9.ui.MainViewModel  WeekBoundaryUtils $com.example.habits9.ui.MainViewModel  
WeekFields $com.example.habits9.ui.MainViewModel  com $com.example.habits9.ui.MainViewModel  getCurrentWeekDates $com.example.habits9.ui.MainViewModel  
getWeekEnd $com.example.habits9.ui.MainViewModel  
getWeekNumber $com.example.habits9.ui.MainViewModel  getWeekStart $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  TemporalAdjusters 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
WeekFields 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getCurrentWeekDates 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekEnd 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekNumber 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getWeekStart 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  map 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  example (com.example.habits9.ui.MainViewModel.com  habits9 0com.example.habits9.ui.MainViewModel.com.example  data 8com.example.habits9.ui.MainViewModel.com.example.habits9  UserPreferencesRepository =com.example.habits9.ui.MainViewModel.com.example.habits9.data  firstDayOfWeek com.example.habits9.ui.WeekInfo  String com.example.habits9.ui.home  
WeekFields java.time.temporal  
nextOrSame $java.time.temporal.TemporalAdjusters                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          